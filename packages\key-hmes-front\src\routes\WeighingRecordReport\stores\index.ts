import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.hmes.weighingrecordreport';
const tenantId = getCurrentOrganizationId();

export const searchDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      required: true,
      label: intl.get(`${modelPrompt}.dateFrom`).d('开始时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      required: true,
      label: intl.get(`${modelPrompt}.dateTo`).d('结束时间'),
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      lovCode: 'HME.PERMISSION_PROD_LINE',
      ignore: FieldIgnore.always,
      required: true,
      label: intl.get(`${modelPrompt}.productLine`).d('产线'),
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'periodLov',
      type: FieldType.object,
      lovCode: 'HME.DST.REPORT_TEAM',
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.period`).d('班组'),
      textField: 'shiftTeamName',
      lovPara: { tenantId },
      dynamicProps: {
        lovPara: ({ record }) => {
          const prodLineLov = record.get('prodLineLov');
          return {
            prodLineId: prodLineLov?.prodLineId,
          };
        },
        disabled: ({ record }) => {
          const prodLineLov = record.get('prodLineLov');
          return !prodLineLov?.prodLineId;
        },
      },
    },
    {
      name: 'shiftTeamName',
      bind: 'periodLov.shiftTeamName',
    },
    {
      name: 'shiftCode',
      bind: 'periodLov.shiftTeamCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    // {
    //   name: 'productionRollNoLov',
    //   type: FieldType.object,
    //   lovCode: 'HME.DST.PRODUCTION_ROLL_NO',
    //   ignore: FieldIgnore.always,
    //   label: intl.get(`${modelPrompt}.productionRollNo`).d('生产卷号'),
    // },
    // {
    //   name: 'productionRollNo',
    //   bind: 'productionRollNoLov.productionRollNo',
    // },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
  ],
});

// 产品信息生产计划表DataSet
export const productionPlanDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/task-output/query/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    { name: 'prodLineCode', type: FieldType.string, label: '产线' },
    { name: 'materialCode', type: FieldType.string, label: '产品编码' },
    { name: 'materialName', type: FieldType.string, label: '产品名称' },
    { name: 'productionQty', type: FieldType.string, label: '计划卷数' },
    { name: 'prodTargetValue', type: FieldType.string, label: '生产目标值' },
    { name: 'productionMeter', type: FieldType.string, label: '生产米数' },
    { name: 'metersPerRoll', type: FieldType.string, label: '每卷米数' },
    { name: 'completeNum', type: FieldType.number, label: '完成卷数' },
    { name: 'prodTaskNum', type: FieldType.string, label: '拼卷任务号' },
    { name: 'customerName', type: FieldType.string, label: '终端客户' },
    { name: 'prodTechnicalRequirements', type: FieldType.string, label: '生产技术要求' },
    { name: 'packingRequirement', type: FieldType.string, label: '包装要求' },
    { name: 'packingMethodName', type: FieldType.string, label: '包装方式' },
    { name: 'workOrderNum', type: FieldType.string, label: '生产指令单号' },
    { name: 'splitQty', type: FieldType.string, label: '报废卷数' },
    { name: 'reelOrderMarkers', type: FieldType.string, label: '卷号定作标记' },
  ],
});

// 卷号信息生产详情表DataSet
export const rollInfoDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/measurement/record/query/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    { name: 'shiftDate', type: FieldType.string, label: '日期' },
    { name: 'shiftCode', type: FieldType.string, label: '班次' },
    { name: 'shiftTeamName', type: FieldType.string, label: '班组' },
    { name: 'identification', type: FieldType.string, label: '销售卷号' },
    { name: 'materialLotCode', type: FieldType.string, label: '生产卷号' },
    { name: 'oldVolumeNumber', type: FieldType.string, label: '翻卷卷号' },
    { name: 'dispositionFunction', type: FieldType.string, label: '卷状态' },
    { name: 'materialName', type: FieldType.string, label: '物料名称' },
    { name: 'prodTargetValue', type: FieldType.number, label: '目标值' },
    { name: 'productionMeter', type: FieldType.number, label: '米数' },
    { name: 'squareMeters', type: FieldType.number, label: '平方数' },
    { name: 'rollUpTime', type: FieldType.string, label: '上卷时间' },
    { name: 'rollDownTime', type: FieldType.string, label: '下卷时间' },
    { name: 'volumeSequenceNum', type: FieldType.number, label: '拼卷顺序' },
    { name: 'backgroup', type: FieldType.string, label: '边角料' },
    { name: 'backqty', type: FieldType.number, label: '重量' },
    { name: 'defectInformation1', type: FieldType.number, label: '1' },
    { name: 'defectInformation2', type: FieldType.number, label: '2' },
    { name: 'defectInformation3', type: FieldType.number, label: '3' },
    { name: 'defectInformation4', type: FieldType.number, label: '4' },
    { name: 'joint1', type: FieldType.number, label: '1' },
    { name: 'joint2', type: FieldType.number, label: '2' },
    { name: 'packingMethodName', type: FieldType.string, label: '包装方式' },
    { name: 'customerName', type: FieldType.string, label: '客户' },
    { name: 'grossWeight', type: FieldType.number, label: '毛重' },
    { name: 'corewEight', type: FieldType.number, label: '管芯' },
    { name: 'isolationMembraneWeight', type: FieldType.number, label: '隔离膜' },
    { name: 'aluminizingMembraneWeight', type: FieldType.number, label: '镀铝膜' },
    { name: 'netWeight', type: FieldType.number, label: '净重' },
    { name: 'detectionResults', type: FieldType.string, label: '结果' },
    { name: 'coilingDiameter', type: FieldType.number, label: '卷径' },
    { name: 'volumeWidth', type: FieldType.number, label: '幅宽测量' },
    { name: 'surfaceFlatness', type: FieldType.string, label: '表面平整度' },
    { name: 'crossSectionFlatness', type: FieldType.string, label: '断面平整度' },
    { name: 'selfTest1Name', type: FieldType.string, label: '自检人1' },
    { name: 'selfTest2Name', type: FieldType.string, label: '自检人2' },
    { name: 'lotNum', type: FieldType.string, label: '隔离膜卷号' },
    { name: 'isolationMembraneTestName', type: FieldType.string, label: '隔离膜自检' },
    { name: 'inInventoryNum', type: FieldType.number, label: '入库数' },
    { name: 'locatorType', type: FieldType.string, label: '是否入库' },
    { name: 'netWeight', type: FieldType.string, label: '用途' },
    { name: 'receiveMeter', type: FieldType.number, label: '领用米数' },
    { name: 'isSummary', type: FieldType.boolean, label: '是否汇总行' },
  ],
});

// 交接记录表DataSet
export const handoverRecordDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/handover/record/query/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    { name: 'recordNo', type: FieldType.number, label: '交接记录' },
    { name: 'prodLineCode', type: FieldType.string, label: '产线' },
    { name: 'shiftDate', type: FieldType.string, label: '日期' },
    { name: 'shiftCode', type: FieldType.string, label: '班次' },
    { name: 'shiftTeamName', type: FieldType.string, label: '班组' },
    { name: 'materialName', type: FieldType.string, label: '物料名称' },
    { name: 'identification', type: FieldType.string, label: '销售卷号' },
    { name: 'identificationProduction', type: FieldType.string, label: '生产卷号' },
    { name: 'rollUpTime', type: FieldType.string, label: '上卷时间' },
    { name: 'transitionMeter', type: FieldType.number, label: '交接米数' },
    { name: 'transitionWight', type: FieldType.number, label: '交接重量' },
    { name: 'selfTest1Name', type: FieldType.string, label: '自检人1' },
    { name: 'selfTest2Name', type: FieldType.string, label: '自检人2' },
    { name: 'isolationMembraneTestName', type: FieldType.string, label: '隔离自检人' },
    { name: 'isSummary', type: FieldType.boolean, label: '是否汇总行' },
  ],
});

export const optionsDs = new DataSet({
  autoQuery: true,
  autoCreate: true,
  selection: false,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'employeeId',
      type: FieldType.number,
    },
    {
      name: 'employeeName',
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-production-report/employee-clock/lov/ui`,
        method: 'GET',
      };
    },
  },
});

export const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'rollUpTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.rollUpTime`).d('上卷时间'),
      dynamicProps: {
        disabled: ({ record }) => record?.get('handoverMark') === 'Y',
      },
    },
    {
      name: 'rollDownTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.rollDownTime`).d('下卷时间'),
    },
    {
      name: 'netWeight',
      type: FieldType.number,
      min: 0,
      disabled: true,
      label: intl.get(`${modelPrompt}.netWeight`).d('净重'),
    },
    {
      name: 'grossWeight',
      min: 0,
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.grossWeight`).d('毛重'),
    },
    {
      name: 'defectInformation1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation1`).d('杂质1'),
    },
    {
      name: 'defectInformation2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation2`).d('杂质2'),
    },
    {
      name: 'defectInformation3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation3`).d('杂质3'),
    },
    {
      name: 'defectInformation4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation4`).d('杂质4'),
    },
    {
      name: 'joint1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.jointOne`).d('接头1'),
    },
    {
      name: 'joint2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.jointTwo`).d('接头2'),
    },
    {
      name: 'volumeWidth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.volumeWidth`).d('卷中宽幅'),
    },
    {
      name: 'coilingDiameter',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.coilingDiameter`).d('卷径'),
    },
    {
      name: 'selfTest1Name',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.selfTest1Name`).d('自检人1'),
      textField: 'employeeName',
      valueField: 'employeeId',
      options: optionsDs,
    },
    {
      name: 'selfTest1',
      bind: 'selfTest1Name.employeeId',
    },
    {
      name: 'selfTest2Name',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.selfTest2Name`).d('自检人2'),
      textField: 'employeeName',
      valueField: 'employeeId',
      options: optionsDs,
    },
    {
      name: 'selfTest2',
      bind: 'selfTest2Name.employeeId',
    },
    {
      name: 'isolationMembraneTestName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.isolationMembraneTestName`).d('隔离膜自检人'),
      options: optionsDs,
      textField: 'employeeName',
      valueField: 'employeeId',
    },
    {
      name: 'isolationMembraneTest1',
      bind: 'isolationMembraneTestName.employeeId',
    },
    {
      name: 'inspectorBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorBy`).d('检验员'),
      options: optionsDs,
      textField: 'employeeName',
      valueField: 'employeeId',
    },
    {
      name: 'inspectorId',
      bind: 'inspectorBy.employeeId',
    },
    {
      name: 'identification',
      disabled: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('卷号'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'materialLotId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批次ID'),
    },
    {
      name: 'shiftTeamActualId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftTeamActualId`).d('开班实绩ID'),
    },
    {
      name: 'prodLineId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineId`).d('产线ID'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
    },
    {
      name: 'shiftTeamId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftTeamId`).d('班组ID'),
    },
    {
      name: 'totalOutput',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.totalOutput`).d('产出总量调整'),
    },
    {
      name: 'shiftUsefulOutput',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftUsefulOutput`).d('有效产量调整'),
    },
  ],
});

export const returnAdjustDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    { name: 'ident', type: FieldType.string, label: '回料标识' },
    { name: 'priQty', type: FieldType.number, label: '回料主单位数量' },
    { name: 'netWeight', type: FieldType.number, label: '回料净重' },
    { name: 'grossWeight', type: FieldType.number, label: '回料毛重' },
    { name: 'adjustQty', type: FieldType.number, label: '数量' },
  ],
});

// 交接卷调整
export const handoverAdjustDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  paging: false,
  fields: [
    { name: 'salesRollNo', type: FieldType.string, label: '销售卷号' },
    { name: 'productionRollNo', type: FieldType.string, label: '生产卷号' },
    { name: 'shiftTeam', type: FieldType.string, label: '班组' },
    { name: 'shiftCode', type: FieldType.string, label: '班次' },
    { name: 'handoverDate', type: FieldType.string, label: '日期' },
    { name: 'materialName', type: FieldType.string, label: '物料名称' },
    { name: 'loadTime', type: FieldType.string, label: '上卷时间' },
    { name: 'handoverMeters', type: FieldType.number, label: '交接米数' },
    { name: 'handoverWeight', type: FieldType.number, label: '交接重量' },
  ],
});
